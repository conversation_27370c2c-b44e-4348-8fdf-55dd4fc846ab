# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
build/
out/
.output/
.vite/
.next/
.nuxt/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov
.nyc_output/

# 测试输出
test-results/
playwright-report/
test-results.xml

# IDE 和编辑器
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# 临时文件
*.tmp
*.temp
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# TypeScript
*.tsbuildinfo

# Vite
.vite/
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# React
.react/

# 包管理器
.pnpm-store/
.yarn/
.yarnrc.yml

# 本地数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 用户上传的文件
uploads/
public/uploads/

# 本地配置文件
config/local.json
config/development.json
config/production.json

# 证书文件
*.pem
*.key
*.crt
*.cert

# 文档生成
docs/build/
.docusaurus/

# Storybook
storybook-static/

# 性能分析
.clinic/
profile/

# 其他
.turbo/
.vercel/
.netlify/
