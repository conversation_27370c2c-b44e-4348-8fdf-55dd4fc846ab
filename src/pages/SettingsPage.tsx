import React, { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Button,
  Form,
  Switch,
  Select,
  Slider,
  Input,
  Divider,
  message,
  Space,
  Modal,
  Upload,
  Progress,
  Alert
} from 'antd'
import {
  SettingOutlined,
  SaveOutlined,
  ExportOutlined,
  ImportOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { documentDAO } from '@services/database/documentDAO'
import { linkService } from '@services/link/linkService'

const { Title, Paragraph, Text } = Typography
const { confirm } = Modal

/**
 * 应用设置接口
 */
interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  autoSave: boolean
  autoSaveInterval: number
  editorFontSize: number
  editorLineHeight: number
  editorWordWrap: boolean
  showLineNumbers: boolean
  enableVimMode: boolean
  enableSpellCheck: boolean
}

/**
 * 默认设置
 */
const DEFAULT_SETTINGS: AppSettings = {
  theme: 'light',
  language: 'zh-CN',
  autoSave: true,
  autoSaveInterval: 30,
  editorFontSize: 14,
  editorLineHeight: 1.5,
  editorWordWrap: true,
  showLineNumbers: true,
  enableVimMode: false,
  enableSpellCheck: true
}

/**
 * 设置页面组件
 * 提供完整的应用设置功能
 */
const SettingsPage: React.FC = () => {
  const [form] = Form.useForm()
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS)
  const [loading, setLoading] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [importProgress, setImportProgress] = useState(0)

  /**
   * 加载设置
   */
  useEffect(() => {
    const loadSettings = () => {
      try {
        const savedSettings = localStorage.getItem('app-settings')
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings)
          setSettings({ ...DEFAULT_SETTINGS, ...parsed })
          form.setFieldsValue({ ...DEFAULT_SETTINGS, ...parsed })
        } else {
          form.setFieldsValue(DEFAULT_SETTINGS)
        }
      } catch (error) {
        console.error('加载设置失败:', error)
        form.setFieldsValue(DEFAULT_SETTINGS)
      }
    }

    loadSettings()
  }, [form])

  /**
   * 保存设置
   */
  const saveSettings = async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()

      localStorage.setItem('app-settings', JSON.stringify(values))
      setSettings(values)

      message.success('设置保存成功')
    } catch (error) {
      console.error('保存设置失败:', error)
      message.error('保存设置失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 重置设置
   */
  const resetSettings = () => {
    confirm({
      title: '确认重置',
      icon: <ExclamationCircleOutlined />,
      content: '确定要重置所有设置到默认值吗？此操作不可撤销。',
      onOk: () => {
        form.setFieldsValue(DEFAULT_SETTINGS)
        setSettings(DEFAULT_SETTINGS)
        localStorage.removeItem('app-settings')
        message.success('设置已重置')
      }
    })
  }

  /**
   * 导出数据
   */
  const exportData = async () => {
    try {
      setExportProgress(0)

      // 获取所有文档
      setExportProgress(20)
      const documents = await documentDAO.getAll()

      // 获取所有链接
      setExportProgress(40)
      const linkStats = await linkService.getLinkStats()

      // 构建导出数据
      setExportProgress(60)
      const exportData = {
        version: '1.0.0',
        exportedAt: new Date().toISOString(),
        documents,
        links: [], // 这里需要获取所有链接数据
        settings,
        metadata: {
          totalDocuments: documents.length,
          totalLinks: linkStats.totalLinks
        }
      }

      // 创建下载
      setExportProgress(80)
      const jsonData = JSON.stringify(exportData, null, 2)
      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.download = `multidimensional-notes-backup-${new Date().toISOString().split('T')[0]}.json`
      link.href = url
      link.click()
      URL.revokeObjectURL(url)

      setExportProgress(100)
      message.success('数据导出成功')

      setTimeout(() => setExportProgress(0), 2000)
    } catch (error) {
      console.error('导出数据失败:', error)
      message.error('导出数据失败')
      setExportProgress(0)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <Title level={2}>
          <SettingOutlined className="mr-2" />
          应用设置
        </Title>
        <Paragraph className="text-gray-600">
          自定义您的多维度笔记应用体验
        </Paragraph>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onFinish={saveSettings}
      >
        {/* 外观设置 */}
        <Card title="外观设置" className="mb-6">
          <Form.Item
            name="theme"
            label="主题模式"
            tooltip="选择应用的外观主题"
          >
            <Select>
              <Select.Option value="light">浅色模式</Select.Option>
              <Select.Option value="dark">深色模式</Select.Option>
              <Select.Option value="auto">跟随系统</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="language"
            label="界面语言"
          >
            <Select>
              <Select.Option value="zh-CN">简体中文</Select.Option>
              <Select.Option value="en-US">English</Select.Option>
            </Select>
          </Form.Item>
        </Card>

        {/* 编辑器设置 */}
        <Card title="编辑器设置" className="mb-6">
          <Form.Item
            name="editorFontSize"
            label="字体大小"
          >
            <Slider
              min={12}
              max={24}
              marks={{
                12: '12px',
                16: '16px',
                20: '20px',
                24: '24px'
              }}
            />
          </Form.Item>

          <Form.Item
            name="editorLineHeight"
            label="行高"
          >
            <Slider
              min={1.2}
              max={2.0}
              step={0.1}
              marks={{
                1.2: '1.2',
                1.5: '1.5',
                1.8: '1.8',
                2.0: '2.0'
              }}
            />
          </Form.Item>

          <Form.Item
            name="editorWordWrap"
            label="自动换行"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="showLineNumbers"
            label="显示行号"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enableVimMode"
            label="启用Vim模式"
            valuePropName="checked"
            tooltip="为高级用户提供Vim键位支持"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enableSpellCheck"
            label="拼写检查"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* 自动保存设置 */}
        <Card title="自动保存" className="mb-6">
          <Form.Item
            name="autoSave"
            label="启用自动保存"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="autoSaveInterval"
            label="自动保存间隔（秒）"
          >
            <Slider
              min={10}
              max={300}
              marks={{
                10: '10s',
                30: '30s',
                60: '1min',
                300: '5min'
              }}
            />
          </Form.Item>
        </Card>

        {/* 数据管理 */}
        <Card title="数据管理" className="mb-6">
          <Space direction="vertical" className="w-full">
            <div>
              <Title level={5}>数据备份</Title>
              <Paragraph className="text-gray-600">
                导出所有文档、链接和设置数据
              </Paragraph>
              <Button
                icon={<ExportOutlined />}
                onClick={exportData}
                loading={exportProgress > 0}
              >
                导出数据
              </Button>
              {exportProgress > 0 && (
                <Progress
                  percent={exportProgress}
                  size="small"
                  className="mt-2"
                />
              )}
            </div>

            <Divider />

            <div>
              <Title level={5}>数据恢复</Title>
              <Paragraph className="text-gray-600">
                从备份文件恢复数据
              </Paragraph>
              <Button icon={<ImportOutlined />} disabled>
                导入数据 (开发中)
              </Button>
            </div>
          </Space>
        </Card>

        {/* 操作按钮 */}
        <Card>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              htmlType="submit"
              loading={loading}
            >
              保存设置
            </Button>
            <Button onClick={resetSettings}>
              重置为默认
            </Button>
          </Space>
        </Card>
      </Form>
    </div>
  )
}

export default SettingsPage
