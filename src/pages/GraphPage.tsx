import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, Typography, Button, Spin, message, Space, Statistic, Row, Col } from 'antd'
import {
  ShareAltOutlined,
  SaveOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined
} from '@ant-design/icons'
import GraphVisualization from '@components/graph/GraphVisualization'
import { documentDAO } from '@services/database/documentDAO'
import { linkService } from '@services/link/linkService'
import { BaseDocument, DocumentLink, DocumentType } from '@types/index'

const { Title, Text } = Typography

/**
 * 关系图谱页面组件
 * 提供完整的关系图谱可视化功能
 */
const GraphPage: React.FC = () => {
  const navigate = useNavigate()

  const [documents, setDocuments] = useState<BaseDocument[]>([])
  const [links, setLinks] = useState<DocumentLink[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<{
    totalDocuments: number
    totalLinks: number
    documentsByType: Record<DocumentType, number>
    orphanDocuments: number
  } | null>(null)

  /**
   * 加载图谱数据
   */
  const loadGraphData = useCallback(async () => {
    try {
      setLoading(true)

      // 并行加载文档和链接数据
      const [allDocuments, linkStats] = await Promise.all([
        documentDAO.getAll(),
        linkService.getLinkStats()
      ])

      // 获取所有链接
      const allLinks: DocumentLink[] = []
      for (const doc of allDocuments) {
        const { outgoing } = await linkService.getLinkedDocuments(doc.id)
        allLinks.push(...outgoing.map(item => item.link))
      }

      setDocuments(allDocuments)
      setLinks(allLinks)

      // 计算统计信息
      const documentsByType = allDocuments.reduce((acc, doc) => {
        acc[doc.type] = (acc[doc.type] || 0) + 1
        return acc
      }, {} as Record<DocumentType, number>)

      setStats({
        totalDocuments: allDocuments.length,
        totalLinks: allLinks.length,
        documentsByType,
        orphanDocuments: linkStats.orphanDocuments.length
      })

      console.log(`图谱数据加载完成: ${allDocuments.length} 个文档, ${allLinks.length} 个链接`)
    } catch (error) {
      console.error('加载图谱数据失败:', error)
      message.error('加载图谱数据失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 处理节点点击
   */
  const handleNodeClick = useCallback((document: BaseDocument) => {
    console.log('点击节点:', document.title)
    message.info(`选中文档: ${document.title}`)
  }, [])

  /**
   * 处理节点双击
   */
  const handleNodeDoubleClick = useCallback((document: BaseDocument) => {
    console.log('双击节点:', document.title)

    // 根据文档类型导航到相应页面
    switch (document.type) {
      case DocumentType.TEXT:
        navigate(`/editor/${document.id}`)
        break
      case DocumentType.WHITEBOARD:
        navigate(`/whiteboard/${document.id}`)
        break
      case DocumentType.MINDMAP:
        navigate(`/mindmap/${document.id}`)
        break
      case DocumentType.KANBAN:
        navigate(`/kanban/${document.id}`)
        break
      default:
        message.warning('未知的文档类型')
    }
  }, [navigate])

  /**
   * 导出图谱数据
   */
  const exportGraphData = useCallback(() => {
    try {
      const exportData = {
        documents: documents.map(doc => ({
          id: doc.id,
          title: doc.title,
          type: doc.type,
          createdAt: doc.createdAt,
          tags: doc.tags
        })),
        links: links.map(link => ({
          id: link.id,
          sourceId: link.sourceId,
          targetId: link.targetId,
          type: link.type,
          label: link.label
        })),
        exportedAt: new Date().toISOString()
      }

      const jsonData = JSON.stringify(exportData, null, 2)
      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.download = `knowledge-graph-${new Date().toISOString().split('T')[0]}.json`
      link.href = url
      link.click()
      URL.revokeObjectURL(url)

      message.success('图谱数据导出成功')
    } catch (error) {
      console.error('导出图谱数据失败:', error)
      message.error('导出图谱数据失败')
    }
  }, [documents, links])

  /**
   * 初始化加载数据
   */
  useEffect(() => {
    loadGraphData()
  }, [loadGraphData])

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载关系图谱中..." />
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col p-6">
      {/* 页面标题和操作栏 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2} className="mb-0">
            <ShareAltOutlined className="mr-2" />
            知识关系图谱
          </Title>
          <Text type="secondary">
            可视化展示文档间的关联关系，发现知识网络结构
          </Text>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadGraphData}
            loading={loading}
          >
            刷新数据
          </Button>
          <Button
            icon={<SaveOutlined />}
            onClick={exportGraphData}
            type="primary"
          >
            导出图谱
          </Button>
        </Space>
      </div>

      {/* 统计信息面板 */}
      {stats && (
        <Card className="mb-6">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="总文档数"
                value={stats.totalDocuments}
                prefix={<FileTextOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="总链接数"
                value={stats.totalLinks}
                prefix={<ShareAltOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="孤立文档"
                value={stats.orphanDocuments}
                valueStyle={{ color: stats.orphanDocuments > 0 ? '#cf1322' : undefined }}
              />
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">
                  {stats.totalLinks > 0 ? (stats.totalLinks / stats.totalDocuments).toFixed(1) : '0'}
                </div>
                <div className="text-sm text-gray-500">平均链接密度</div>
              </div>
            </Col>
          </Row>

          <div className="mt-4 pt-4 border-t">
            <Text strong>文档类型分布: </Text>
            <Space>
              {Object.entries(stats.documentsByType).map(([type, count]) => (
                <span key={type} className="inline-flex items-center gap-1">
                  {type === DocumentType.TEXT && <FileTextOutlined />}
                  {type === DocumentType.WHITEBOARD && <BgColorsOutlined />}
                  {type === DocumentType.MINDMAP && <NodeIndexOutlined />}
                  {type === DocumentType.KANBAN && <ProjectOutlined />}
                  <Text>
                    {type === DocumentType.TEXT && '文本'}
                    {type === DocumentType.WHITEBOARD && '白板'}
                    {type === DocumentType.MINDMAP && '思维导图'}
                    {type === DocumentType.KANBAN && '看板'}
                    : {count}
                  </Text>
                </span>
              ))}
            </Space>
          </div>
        </Card>
      )}

      {/* 图谱可视化 */}
      <div className="flex-1">
        {documents.length > 0 ? (
          <GraphVisualization
            documents={documents}
            links={links}
            onNodeClick={handleNodeClick}
            onNodeDoubleClick={handleNodeDoubleClick}
            className="h-full"
          />
        ) : (
          <Card className="h-full flex items-center justify-center">
            <div className="text-center">
              <ShareAltOutlined className="text-6xl text-gray-400 mb-4" />
              <Title level={3} type="secondary">暂无数据</Title>
              <Text type="secondary">
                创建一些文档并建立链接关系后，图谱将在这里显示
              </Text>
            </div>
          </Card>
        )}
      </div>
    </div>
  )
}

export default GraphPage
