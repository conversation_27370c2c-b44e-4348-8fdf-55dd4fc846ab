/**
 * 白板服务测试
 */

import { whiteboardService } from '@services/whiteboardService'
import { documentDAO } from '@services/database/documentDAO'
import { DocumentType } from '@types/index'

// Mock documentDAO
jest.mock('@services/database/documentDAO')
const mockDocumentDAO = documentDAO as jest.Mocked<typeof documentDAO>

describe('WhiteboardService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('createWhiteboard', () => {
    it('应该创建新的白板文档', async () => {
      // 准备测试数据
      const title = '测试白板'
      
      // 模拟数据库操作
      mockDocumentDAO.create.mockResolvedValue(undefined)

      // 执行测试
      const result = await whiteboardService.createWhiteboard(title)

      // 验证结果
      expect(result).toBeDefined()
      expect(result.title).toBe(title)
      expect(result.type).toBe(DocumentType.WHITEBOARD)
      expect(result.content).toBeDefined()
      expect(result.content.canvas).toBeDefined()
      expect(result.content.objects).toEqual([])
      expect(result.metadata.elementCount).toBe(0)

      // 验证数据库调用
      expect(mockDocumentDAO.create).toHaveBeenCalledTimes(1)
      expect(mockDocumentDAO.create).toHaveBeenCalledWith(
        expect.objectContaining({
          type: DocumentType.WHITEBOARD,
          title
        })
      )
    })

    it('应该使用默认标题创建白板', async () => {
      mockDocumentDAO.create.mockResolvedValue(undefined)

      const result = await whiteboardService.createWhiteboard()

      expect(result.title).toBe('新建白板')
    })
  })

  describe('saveWhiteboard', () => {
    it('应该保存白板文档并更新元数据', async () => {
      // 准备测试数据
      const whiteboard = await whiteboardService.createWhiteboard('测试白板')
      whiteboard.content.objects = [
        {
          id: 'obj1',
          type: 'rectangle',
          x: 10,
          y: 10,
          width: 100,
          height: 50
        }
      ] as any

      mockDocumentDAO.update.mockResolvedValue(undefined)

      // 执行测试
      await whiteboardService.saveWhiteboard(whiteboard)

      // 验证数据库调用
      expect(mockDocumentDAO.update).toHaveBeenCalledTimes(1)
      expect(mockDocumentDAO.update).toHaveBeenCalledWith(
        expect.objectContaining({
          type: DocumentType.WHITEBOARD,
          metadata: expect.objectContaining({
            elementCount: 1
          })
        })
      )
    })
  })

  describe('loadWhiteboard', () => {
    it('应该加载存在的白板文档', async () => {
      // 准备测试数据
      const mockDoc = {
        id: 'test-id',
        type: DocumentType.WHITEBOARD,
        title: '测试白板',
        content: {
          canvas: { width: 1920, height: 1080, backgroundColor: '#ffffff' },
          objects: [],
          viewport: { x: 0, y: 0, zoom: 1 },
          layers: []
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        metadata: { elementCount: 0 }
      }

      mockDocumentDAO.getById.mockResolvedValue(mockDoc)

      // 执行测试
      const result = await whiteboardService.loadWhiteboard('test-id')

      // 验证结果
      expect(result).toBeDefined()
      expect(result!.id).toBe('test-id')
      expect(result!.type).toBe(DocumentType.WHITEBOARD)
      expect(result!.title).toBe('测试白板')

      // 验证数据库调用
      expect(mockDocumentDAO.getById).toHaveBeenCalledWith('test-id')
    })

    it('应该在文档不存在时返回null', async () => {
      mockDocumentDAO.getById.mockResolvedValue(null)

      const result = await whiteboardService.loadWhiteboard('non-existent')

      expect(result).toBeNull()
    })

    it('应该在文档类型不匹配时返回null', async () => {
      const mockDoc = {
        id: 'test-id',
        type: DocumentType.TEXT,
        title: '文本文档',
        content: 'test content',
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        metadata: {}
      }

      mockDocumentDAO.getById.mockResolvedValue(mockDoc)

      const result = await whiteboardService.loadWhiteboard('test-id')

      expect(result).toBeNull()
    })
  })

  describe('deleteWhiteboard', () => {
    it('应该删除白板文档', async () => {
      mockDocumentDAO.delete.mockResolvedValue(undefined)

      const result = await whiteboardService.deleteWhiteboard('test-id')

      expect(result).toBe(true)
      expect(mockDocumentDAO.delete).toHaveBeenCalledWith('test-id')
    })

    it('应该在删除失败时返回false', async () => {
      mockDocumentDAO.delete.mockRejectedValue(new Error('删除失败'))

      const result = await whiteboardService.deleteWhiteboard('test-id')

      expect(result).toBe(false)
    })
  })

  describe('getWhiteboardList', () => {
    it('应该返回白板文档列表', async () => {
      const mockDocs = [
        {
          id: 'wb1',
          type: DocumentType.WHITEBOARD,
          title: '白板1',
          content: { canvas: {}, objects: [], viewport: {}, layers: [] },
          createdAt: new Date(),
          updatedAt: new Date(),
          tags: [],
          metadata: { elementCount: 0 }
        },
        {
          id: 'wb2',
          type: DocumentType.WHITEBOARD,
          title: '白板2',
          content: { canvas: {}, objects: [], viewport: {}, layers: [] },
          createdAt: new Date(),
          updatedAt: new Date(),
          tags: [],
          metadata: { elementCount: 5 }
        }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockDocs)

      const result = await whiteboardService.getWhiteboardList()

      expect(result).toHaveLength(2)
      expect(result[0].id).toBe('wb1')
      expect(result[1].id).toBe('wb2')
      expect(mockDocumentDAO.getByType).toHaveBeenCalledWith(DocumentType.WHITEBOARD)
    })

    it('应该在获取失败时返回空数组', async () => {
      mockDocumentDAO.getByType.mockRejectedValue(new Error('获取失败'))

      const result = await whiteboardService.getWhiteboardList()

      expect(result).toEqual([])
    })
  })

  describe('duplicateWhiteboard', () => {
    it('应该复制白板文档', async () => {
      // 准备原始白板
      const originalWhiteboard = {
        id: 'original-id',
        type: DocumentType.WHITEBOARD,
        title: '原始白板',
        content: {
          canvas: { width: 1920, height: 1080, backgroundColor: '#ffffff' },
          objects: [{ id: 'obj1', type: 'rectangle' }],
          viewport: { x: 0, y: 0, zoom: 1 },
          layers: []
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: ['tag1'],
        metadata: { elementCount: 1 }
      }

      // 模拟数据库操作
      mockDocumentDAO.getById.mockResolvedValue(originalWhiteboard)
      mockDocumentDAO.create.mockResolvedValue(undefined)
      mockDocumentDAO.update.mockResolvedValue(undefined)

      // 执行测试
      const result = await whiteboardService.duplicateWhiteboard('original-id', '复制的白板')

      // 验证结果
      expect(result).toBeDefined()
      expect(result!.title).toBe('复制的白板')
      expect(result!.content.objects).toHaveLength(1)
      expect(result!.content.objects[0].id).not.toBe('obj1') // ID应该被重新生成
      expect(result!.tags).toEqual(['tag1'])

      // 验证数据库调用
      expect(mockDocumentDAO.create).toHaveBeenCalledTimes(2) // 创建新白板 + 保存复制内容
      expect(mockDocumentDAO.update).toHaveBeenCalledTimes(1)
    })

    it('应该在原始白板不存在时返回null', async () => {
      mockDocumentDAO.getById.mockResolvedValue(null)

      const result = await whiteboardService.duplicateWhiteboard('non-existent')

      expect(result).toBeNull()
    })
  })

  describe('searchWhiteboards', () => {
    it('应该根据标题搜索白板', async () => {
      const mockWhiteboards = [
        {
          id: 'wb1',
          title: '项目设计白板',
          tags: ['设计'],
          type: DocumentType.WHITEBOARD,
          content: { canvas: {}, objects: [], viewport: {}, layers: [] },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        },
        {
          id: 'wb2',
          title: '会议记录',
          tags: ['会议'],
          type: DocumentType.WHITEBOARD,
          content: { canvas: {}, objects: [], viewport: {}, layers: [] },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockWhiteboards)

      const result = await whiteboardService.searchWhiteboards('设计')

      expect(result).toHaveLength(1)
      expect(result[0].title).toBe('项目设计白板')
    })

    it('应该根据标签搜索白板', async () => {
      const mockWhiteboards = [
        {
          id: 'wb1',
          title: '白板1',
          tags: ['设计', '原型'],
          type: DocumentType.WHITEBOARD,
          content: { canvas: {}, objects: [], viewport: {}, layers: [] },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockWhiteboards)

      const result = await whiteboardService.searchWhiteboards('原型')

      expect(result).toHaveLength(1)
      expect(result[0].id).toBe('wb1')
    })

    it('应该在查询为空时返回所有白板', async () => {
      const mockWhiteboards = [
        { id: 'wb1', title: '白板1', tags: [], type: DocumentType.WHITEBOARD, content: {}, createdAt: new Date(), updatedAt: new Date(), metadata: {} },
        { id: 'wb2', title: '白板2', tags: [], type: DocumentType.WHITEBOARD, content: {}, createdAt: new Date(), updatedAt: new Date(), metadata: {} }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockWhiteboards)

      const result = await whiteboardService.searchWhiteboards('')

      expect(result).toHaveLength(2)
    })
  })
})
