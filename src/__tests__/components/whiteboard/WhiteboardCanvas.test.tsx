/**
 * 白板画布组件测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import WhiteboardCanvas, { DrawingTool } from '@components/whiteboard/WhiteboardCanvas'

// Mock Canvas API
const mockCanvas = {
  getContext: jest.fn(() => ({
    lineCap: '',
    lineJoin: '',
    strokeStyle: '',
    fillStyle: '',
    lineWidth: 0,
    clearRect: jest.fn(),
    beginPath: jest.fn(),
    moveTo: jest.fn(),
    lineTo: jest.fn(),
    stroke: jest.fn(),
    rect: jest.fn(),
    arc: jest.fn(),
    getImageData: jest.fn(() => ({ data: new Uint8ClampedArray(4) })),
    putImageData: jest.fn()
  })),
  toDataURL: jest.fn(() => 'data:image/png;base64,test'),
  width: 800,
  height: 600
}

// Mock HTMLCanvasElement
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: mockCanvas.getContext
})

Object.defineProperty(HTMLCanvasElement.prototype, 'toDataURL', {
  value: mockCanvas.toDataURL
})

describe('WhiteboardCanvas', () => {
  const mockOnSave = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('应该渲染白板画布', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    expect(screen.getByRole('button', { name: /画笔/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /矩形/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /圆形/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /直线/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /保存/ })).toBeInTheDocument()
  })

  it('应该切换绘图工具', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const penButton = screen.getByRole('button', { name: /画笔/ })
    const rectangleButton = screen.getByRole('button', { name: /矩形/ })
    
    // 默认选中画笔工具
    expect(penButton).toHaveClass('ant-btn-primary')
    
    // 切换到矩形工具
    fireEvent.click(rectangleButton)
    expect(rectangleButton).toHaveClass('ant-btn-primary')
    expect(penButton).not.toHaveClass('ant-btn-primary')
  })

  it('应该调整线条粗细', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const slider = screen.getByRole('slider')
    
    // 调整滑块值
    fireEvent.change(slider, { target: { value: '10' } })
    
    // 验证显示的值
    expect(screen.getByText('10')).toBeInTheDocument()
  })

  it('应该处理画布鼠标事件', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const canvas = screen.getByRole('img', { hidden: true }) // canvas元素
    
    // 模拟鼠标按下
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 })
    
    // 模拟鼠标移动
    fireEvent.mouseMove(canvas, { clientX: 150, clientY: 150 })
    
    // 模拟鼠标释放
    fireEvent.mouseUp(canvas, { clientX: 150, clientY: 150 })
    
    // 验证canvas上下文方法被调用
    const context = mockCanvas.getContext()
    expect(context.beginPath).toHaveBeenCalled()
    expect(context.moveTo).toHaveBeenCalled()
  })

  it('应该清空画布', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const clearButton = screen.getByRole('button', { name: /清空/ })
    
    fireEvent.click(clearButton)
    
    const context = mockCanvas.getContext()
    expect(context.clearRect).toHaveBeenCalledWith(0, 0, 800, 600)
  })

  it('应该保存画布', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const saveButton = screen.getByRole('button', { name: /保存/ })
    
    fireEvent.click(saveButton)
    
    expect(mockOnSave).toHaveBeenCalledWith('data:image/png;base64,test')
  })

  it('应该支持撤销操作', async () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const canvas = screen.getByRole('img', { hidden: true })
    const undoButton = screen.getByRole('button', { name: /撤销/ })
    
    // 初始状态下撤销按钮应该被禁用
    expect(undoButton).toBeDisabled()
    
    // 进行一次绘制操作
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 })
    fireEvent.mouseUp(canvas, { clientX: 150, clientY: 150 })
    
    // 等待状态更新
    await waitFor(() => {
      expect(undoButton).not.toBeDisabled()
    })
    
    // 执行撤销
    fireEvent.click(undoButton)
    
    const context = mockCanvas.getContext()
    expect(context.putImageData).toHaveBeenCalled()
  })

  it('应该支持重做操作', async () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const canvas = screen.getByRole('img', { hidden: true })
    const undoButton = screen.getByRole('button', { name: /撤销/ })
    const redoButton = screen.getByRole('button', { name: /重做/ })
    
    // 进行绘制操作
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 })
    fireEvent.mouseUp(canvas, { clientX: 150, clientY: 150 })
    
    // 撤销操作
    await waitFor(() => {
      expect(undoButton).not.toBeDisabled()
    })
    fireEvent.click(undoButton)
    
    // 重做按钮应该可用
    await waitFor(() => {
      expect(redoButton).not.toBeDisabled()
    })
    
    // 执行重做
    fireEvent.click(redoButton)
    
    const context = mockCanvas.getContext()
    expect(context.putImageData).toHaveBeenCalled()
  })

  it('应该绘制矩形', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const canvas = screen.getByRole('img', { hidden: true })
    const rectangleButton = screen.getByRole('button', { name: /矩形/ })
    
    // 切换到矩形工具
    fireEvent.click(rectangleButton)
    
    // 绘制矩形
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 })
    fireEvent.mouseUp(canvas, { clientX: 200, clientY: 150 })
    
    const context = mockCanvas.getContext()
    expect(context.rect).toHaveBeenCalledWith(100, 100, 100, 50)
    expect(context.stroke).toHaveBeenCalled()
  })

  it('应该绘制圆形', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const canvas = screen.getByRole('img', { hidden: true })
    const circleButton = screen.getByRole('button', { name: /圆形/ })
    
    // 切换到圆形工具
    fireEvent.click(circleButton)
    
    // 绘制圆形
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 })
    fireEvent.mouseUp(canvas, { clientX: 150, clientY: 150 })
    
    const context = mockCanvas.getContext()
    const expectedRadius = Math.sqrt(Math.pow(50, 2) + Math.pow(50, 2)) // 距离公式
    expect(context.arc).toHaveBeenCalledWith(100, 100, expectedRadius, 0, 2 * Math.PI)
    expect(context.stroke).toHaveBeenCalled()
  })

  it('应该绘制直线', () => {
    render(<WhiteboardCanvas onSave={mockOnSave} />)
    
    const canvas = screen.getByRole('img', { hidden: true })
    const lineButton = screen.getByRole('button', { name: /直线/ })
    
    // 切换到直线工具
    fireEvent.click(lineButton)
    
    // 绘制直线
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 })
    fireEvent.mouseUp(canvas, { clientX: 200, clientY: 150 })
    
    const context = mockCanvas.getContext()
    expect(context.moveTo).toHaveBeenCalledWith(100, 100)
    expect(context.lineTo).toHaveBeenCalledWith(200, 150)
    expect(context.stroke).toHaveBeenCalled()
  })

  it('应该使用自定义尺寸', () => {
    render(<WhiteboardCanvas width={1000} height={800} onSave={mockOnSave} />)
    
    const canvas = screen.getByRole('img', { hidden: true })
    
    expect(canvas).toHaveAttribute('width', '1000')
    expect(canvas).toHaveAttribute('height', '800')
  })

  it('应该应用自定义类名', () => {
    render(<WhiteboardCanvas className="custom-whiteboard" onSave={mockOnSave} />)
    
    const container = screen.getByText('粗细:').closest('.whiteboard-canvas')
    
    expect(container).toHaveClass('custom-whiteboard')
  })
})
