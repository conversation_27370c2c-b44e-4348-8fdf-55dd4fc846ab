import React, { useState, useEffect, useCallback } from 'react'
import { 
  Modal, 
  Input, 
  List, 
  Button, 
  Space, 
  Tag, 
  Select, 
  message, 
  Divider,
  Typography,
  Empty,
  Tooltip
} from 'antd'
import { 
  LinkOutlined, 
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined
} from '@ant-design/icons'
import { linkService, LinkSuggestion } from '@services/link/linkService'
import { documentDAO } from '@services/database/documentDAO'
import { BaseDocument, DocumentLink, LinkType, DocumentType } from '@types/index'

const { Text, Title } = Typography
const { Search } = Input

/**
 * 链接管理器组件属性
 */
interface LinkManagerProps {
  visible: boolean
  currentDocumentId: string
  onClose: () => void
  onLinkCreated?: (link: DocumentLink) => void
  onLinkDeleted?: (linkId: string) => void
}

/**
 * 文档类型图标映射
 */
const DOCUMENT_TYPE_ICONS = {
  [DocumentType.TEXT]: <FileTextOutlined />,
  [DocumentType.WHITEBOARD]: <BgColorsOutlined />,
  [DocumentType.MINDMAP]: <NodeIndexOutlined />,
  [DocumentType.KANBAN]: <ProjectOutlined />
}

/**
 * 链接类型标签映射
 */
const LINK_TYPE_LABELS = {
  [LinkType.REFERENCE]: { label: '引用', color: 'blue' },
  [LinkType.RELATED]: { label: '相关', color: 'green' },
  [LinkType.PARENT_CHILD]: { label: '父子', color: 'orange' },
  [LinkType.DEPENDENCY]: { label: '依赖', color: 'red' }
}

/**
 * 链接管理器组件
 * 提供创建、查看、删除文档链接的功能
 */
const LinkManager: React.FC<LinkManagerProps> = ({
  visible,
  currentDocumentId,
  onClose,
  onLinkCreated,
  onLinkDeleted
}) => {
  const [activeTab, setActiveTab] = useState<'existing' | 'create'>('existing')
  const [existingLinks, setExistingLinks] = useState<Array<{ document: BaseDocument; link: DocumentLink }>>([])
  const [searchResults, setSearchResults] = useState<BaseDocument[]>([])
  const [suggestions, setSuggestions] = useState<LinkSuggestion[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLinkType, setSelectedLinkType] = useState<LinkType>(LinkType.REFERENCE)
  const [loading, setLoading] = useState(false)

  /**
   * 加载现有链接
   */
  const loadExistingLinks = useCallback(async () => {
    try {
      setLoading(true)
      const { outgoing, incoming } = await linkService.getLinkedDocuments(currentDocumentId)
      
      // 合并出链和入链，去重
      const allLinks = [...outgoing, ...incoming]
      const uniqueLinks = allLinks.filter((item, index, self) => 
        index === self.findIndex(t => t.link.id === item.link.id)
      )
      
      setExistingLinks(uniqueLinks)
    } catch (error) {
      console.error('加载现有链接失败:', error)
      message.error('加载现有链接失败')
    } finally {
      setLoading(false)
    }
  }, [currentDocumentId])

  /**
   * 加载链接建议
   */
  const loadSuggestions = useCallback(async () => {
    try {
      const suggestions = await linkService.getLinkSuggestions(currentDocumentId, 10)
      setSuggestions(suggestions)
    } catch (error) {
      console.error('加载链接建议失败:', error)
    }
  }, [currentDocumentId])

  /**
   * 搜索文档
   */
  const searchDocuments = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      return
    }

    try {
      const allDocuments = await documentDAO.getAll()
      const filteredDocuments = allDocuments.filter(doc => 
        doc.id !== currentDocumentId &&
        (doc.title.toLowerCase().includes(query.toLowerCase()) ||
         doc.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())))
      )
      
      setSearchResults(filteredDocuments.slice(0, 20)) // 限制结果数量
    } catch (error) {
      console.error('搜索文档失败:', error)
      message.error('搜索文档失败')
    }
  }, [currentDocumentId])

  /**
   * 创建链接
   */
  const createLink = useCallback(async (targetDocumentId: string, linkType: LinkType = selectedLinkType) => {
    try {
      const link = await linkService.createBidirectionalLink(
        currentDocumentId,
        targetDocumentId,
        linkType
      )
      
      message.success('链接创建成功')
      
      if (onLinkCreated) {
        onLinkCreated(link)
      }
      
      // 刷新现有链接列表
      await loadExistingLinks()
      
      // 清空搜索结果
      setSearchQuery('')
      setSearchResults([])
    } catch (error) {
      console.error('创建链接失败:', error)
      message.error('创建链接失败')
    }
  }, [currentDocumentId, selectedLinkType, onLinkCreated, loadExistingLinks])

  /**
   * 删除链接
   */
  const deleteLink = useCallback(async (linkId: string) => {
    try {
      await linkService.deleteLink(linkId)
      message.success('链接删除成功')
      
      if (onLinkDeleted) {
        onLinkDeleted(linkId)
      }
      
      // 刷新现有链接列表
      await loadExistingLinks()
    } catch (error) {
      console.error('删除链接失败:', error)
      message.error('删除链接失败')
    }
  }, [onLinkDeleted, loadExistingLinks])

  /**
   * 处理搜索
   */
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value)
    searchDocuments(value)
  }, [searchDocuments])

  /**
   * 初始化数据
   */
  useEffect(() => {
    if (visible) {
      loadExistingLinks()
      loadSuggestions()
    }
  }, [visible, loadExistingLinks, loadSuggestions])

  /**
   * 渲染文档项
   */
  const renderDocumentItem = useCallback((
    document: BaseDocument, 
    action: 'link' | 'view', 
    link?: DocumentLink
  ) => (
    <List.Item
      key={document.id}
      actions={[
        action === 'link' ? (
          <Button
            type="link"
            icon={<PlusOutlined />}
            onClick={() => createLink(document.id)}
          >
            创建链接
          </Button>
        ) : (
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => link && deleteLink(link.id)}
          >
            删除链接
          </Button>
        )
      ]}
    >
      <List.Item.Meta
        avatar={DOCUMENT_TYPE_ICONS[document.type]}
        title={
          <div className="flex items-center gap-2">
            <span>{document.title}</span>
            {link && (
              <Tag color={LINK_TYPE_LABELS[link.type].color} size="small">
                {LINK_TYPE_LABELS[link.type].label}
              </Tag>
            )}
          </div>
        }
        description={
          <div>
            <Text type="secondary" className="text-sm">
              {document.type === DocumentType.TEXT && '文本文档'}
              {document.type === DocumentType.WHITEBOARD && '白板'}
              {document.type === DocumentType.MINDMAP && '思维导图'}
              {document.type === DocumentType.KANBAN && '看板'}
              {' • '}
              {document.createdAt.toLocaleDateString()}
            </Text>
            {document.tags.length > 0 && (
              <div className="mt-1">
                {document.tags.map(tag => (
                  <Tag key={tag} size="small">{tag}</Tag>
                ))}
              </div>
            )}
          </div>
        }
      />
    </List.Item>
  ), [createLink, deleteLink])

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <LinkOutlined />
          <span>链接管理</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
    >
      <div className="mb-4">
        <Space>
          <Button
            type={activeTab === 'existing' ? 'primary' : 'default'}
            onClick={() => setActiveTab('existing')}
          >
            现有链接 ({existingLinks.length})
          </Button>
          <Button
            type={activeTab === 'create' ? 'primary' : 'default'}
            onClick={() => setActiveTab('create')}
          >
            创建链接
          </Button>
        </Space>
      </div>

      {activeTab === 'existing' && (
        <div>
          <Title level={5}>已关联的文档</Title>
          {existingLinks.length > 0 ? (
            <List
              dataSource={existingLinks}
              renderItem={({ document, link }) => renderDocumentItem(document, 'view', link)}
              loading={loading}
            />
          ) : (
            <Empty 
              description="暂无关联文档"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      )}

      {activeTab === 'create' && (
        <div>
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <span>链接类型:</span>
              <Select
                value={selectedLinkType}
                onChange={setSelectedLinkType}
                style={{ width: 120 }}
              >
                {Object.entries(LINK_TYPE_LABELS).map(([type, config]) => (
                  <Select.Option key={type} value={type}>
                    <Tag color={config.color} size="small">
                      {config.label}
                    </Tag>
                  </Select.Option>
                ))}
              </Select>
            </div>
            
            <Search
              placeholder="搜索要链接的文档..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onSearch={handleSearch}
              enterButton={<SearchOutlined />}
            />
          </div>

          {searchResults.length > 0 && (
            <div className="mb-6">
              <Title level={5}>搜索结果</Title>
              <List
                dataSource={searchResults}
                renderItem={(document) => renderDocumentItem(document, 'link')}
                size="small"
              />
            </div>
          )}

          {suggestions.length > 0 && (
            <div>
              <Title level={5}>
                <Tooltip title="基于内容相似性、标签匹配等因素推荐的相关文档">
                  推荐链接
                </Tooltip>
              </Title>
              <List
                dataSource={suggestions}
                renderItem={(suggestion) => (
                  <List.Item
                    key={suggestion.document.id}
                    actions={[
                      <Button
                        type="link"
                        icon={<PlusOutlined />}
                        onClick={() => createLink(suggestion.document.id, suggestion.type)}
                      >
                        创建链接
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={DOCUMENT_TYPE_ICONS[suggestion.document.type]}
                      title={
                        <div className="flex items-center gap-2">
                          <span>{suggestion.document.title}</span>
                          <Tag color={LINK_TYPE_LABELS[suggestion.type].color} size="small">
                            {LINK_TYPE_LABELS[suggestion.type].label}
                          </Tag>
                          <Tag color="gold" size="small">
                            匹配度: {suggestion.score}
                          </Tag>
                        </div>
                      }
                      description={
                        <div>
                          <Text type="secondary" className="text-sm">
                            {suggestion.reason}
                          </Text>
                          {suggestion.document.tags.length > 0 && (
                            <div className="mt-1">
                              {suggestion.document.tags.map(tag => (
                                <Tag key={tag} size="small">{tag}</Tag>
                              ))}
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
                size="small"
              />
            </div>
          )}

          {searchQuery && searchResults.length === 0 && (
            <Empty 
              description="未找到匹配的文档"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      )}
    </Modal>
  )
}

export default LinkManager
