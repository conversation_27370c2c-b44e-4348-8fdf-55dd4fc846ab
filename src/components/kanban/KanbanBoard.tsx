import React, { useState, useCallback } from 'react'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import { Card, Button, Input, Tag, Space, Modal, Form, Select, DatePicker, message } from 'antd'
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  CalendarOutlined,
  UserOutlined,
  FlagOutlined
} from '@ant-design/icons'
import { generateUUID } from '@utils/index'

/**
 * 看板卡片优先级
 */
export enum CardPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * 看板卡片状态
 */
export enum CardStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  DONE = 'done'
}

/**
 * 看板卡片接口
 */
export interface KanbanCard {
  id: string
  title: string
  description: string
  status: CardStatus
  priority: CardPriority
  tags: string[]
  assignee?: string
  dueDate?: Date
  createdAt: Date
  updatedAt: Date
}

/**
 * 看板列接口
 */
export interface KanbanColumn {
  id: string
  title: string
  status: CardStatus
  cards: KanbanCard[]
  color: string
  limit?: number
}

/**
 * 看板数据接口
 */
export interface KanbanData {
  columns: KanbanColumn[]
  cards: KanbanCard[]
}

/**
 * 看板组件属性
 */
interface KanbanBoardProps {
  data?: KanbanData
  onSave?: (data: KanbanData) => void
  className?: string
}

/**
 * 优先级颜色映射
 */
const PRIORITY_COLORS = {
  [CardPriority.LOW]: '#52c41a',
  [CardPriority.MEDIUM]: '#1890ff',
  [CardPriority.HIGH]: '#fa8c16',
  [CardPriority.URGENT]: '#f5222d'
}

/**
 * 优先级标签映射
 */
const PRIORITY_LABELS = {
  [CardPriority.LOW]: '低',
  [CardPriority.MEDIUM]: '中',
  [CardPriority.HIGH]: '高',
  [CardPriority.URGENT]: '紧急'
}

/**
 * 默认看板数据
 */
const DEFAULT_KANBAN_DATA: KanbanData = {
  columns: [
    {
      id: 'todo',
      title: '待办',
      status: CardStatus.TODO,
      cards: [],
      color: '#f0f0f0'
    },
    {
      id: 'in_progress',
      title: '进行中',
      status: CardStatus.IN_PROGRESS,
      cards: [],
      color: '#e6f7ff'
    },
    {
      id: 'review',
      title: '待审核',
      status: CardStatus.REVIEW,
      cards: [],
      color: '#fff7e6'
    },
    {
      id: 'done',
      title: '已完成',
      status: CardStatus.DONE,
      cards: [],
      color: '#f6ffed'
    }
  ],
  cards: []
}

/**
 * 看板组件
 * 提供完整的看板管理功能，支持拖拽操作
 */
const KanbanBoard: React.FC<KanbanBoardProps> = ({
  data = DEFAULT_KANBAN_DATA,
  onSave,
  className = ''
}) => {
  const [kanbanData, setKanbanData] = useState<KanbanData>(data)
  const [isCardModalVisible, setIsCardModalVisible] = useState(false)
  const [editingCard, setEditingCard] = useState<KanbanCard | null>(null)
  const [selectedColumn, setSelectedColumn] = useState<CardStatus | null>(null)
  const [form] = Form.useForm()

  /**
   * 处理拖拽结束
   */
  const handleDragEnd = useCallback((result: DropResult) => {
    const { destination, source, draggableId } = result

    // 如果没有目标位置，则不执行任何操作
    if (!destination) {
      return
    }

    // 如果位置没有改变，则不执行任何操作
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return
    }

    const sourceColumnId = source.droppableId as CardStatus
    const destColumnId = destination.droppableId as CardStatus

    setKanbanData(prevData => {
      const newData = { ...prevData }
      
      // 找到被拖拽的卡片
      const draggedCard = newData.cards.find(card => card.id === draggableId)
      if (!draggedCard) return prevData

      // 更新卡片状态
      draggedCard.status = destColumnId
      draggedCard.updatedAt = new Date()

      // 重新组织列数据
      newData.columns = newData.columns.map(column => ({
        ...column,
        cards: newData.cards.filter(card => card.status === column.status)
      }))

      return newData
    })

    console.log(`卡片 ${draggableId} 从 ${sourceColumnId} 移动到 ${destColumnId}`)
  }, [])

  /**
   * 打开卡片编辑模态框
   */
  const openCardModal = useCallback((columnStatus?: CardStatus, card?: KanbanCard) => {
    setSelectedColumn(columnStatus || null)
    setEditingCard(card || null)
    
    if (card) {
      form.setFieldsValue({
        title: card.title,
        description: card.description,
        priority: card.priority,
        tags: card.tags,
        assignee: card.assignee,
        dueDate: card.dueDate ? new Date(card.dueDate) : null
      })
    } else {
      form.resetFields()
    }
    
    setIsCardModalVisible(true)
  }, [form])

  /**
   * 关闭卡片编辑模态框
   */
  const closeCardModal = useCallback(() => {
    setIsCardModalVisible(false)
    setEditingCard(null)
    setSelectedColumn(null)
    form.resetFields()
  }, [form])

  /**
   * 保存卡片
   */
  const saveCard = useCallback(async () => {
    try {
      const values = await form.validateFields()
      const now = new Date()

      if (editingCard) {
        // 更新现有卡片
        setKanbanData(prevData => ({
          ...prevData,
          cards: prevData.cards.map(card =>
            card.id === editingCard.id
              ? {
                  ...card,
                  ...values,
                  updatedAt: now
                }
              : card
          )
        }))
        message.success('卡片更新成功')
      } else {
        // 创建新卡片
        const newCard: KanbanCard = {
          id: generateUUID(),
          ...values,
          status: selectedColumn || CardStatus.TODO,
          createdAt: now,
          updatedAt: now
        }

        setKanbanData(prevData => ({
          ...prevData,
          cards: [...prevData.cards, newCard]
        }))
        message.success('卡片创建成功')
      }

      closeCardModal()
    } catch (error) {
      console.error('保存卡片失败:', error)
    }
  }, [editingCard, selectedColumn, form, closeCardModal])

  /**
   * 删除卡片
   */
  const deleteCard = useCallback((cardId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张卡片吗？',
      onOk: () => {
        setKanbanData(prevData => ({
          ...prevData,
          cards: prevData.cards.filter(card => card.id !== cardId)
        }))
        message.success('卡片删除成功')
      }
    })
  }, [])

  /**
   * 保存看板数据
   */
  const saveKanban = useCallback(() => {
    if (onSave) {
      onSave(kanbanData)
    }
  }, [kanbanData, onSave])

  /**
   * 渲染卡片
   */
  const renderCard = useCallback((card: KanbanCard, index: number) => (
    <Draggable key={card.id} draggableId={card.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-3 ${snapshot.isDragging ? 'opacity-50' : ''}`}
        >
          <Card
            size="small"
            className="shadow-sm hover:shadow-md transition-shadow cursor-pointer"
            actions={[
              <EditOutlined 
                key="edit" 
                onClick={(e) => {
                  e.stopPropagation()
                  openCardModal(undefined, card)
                }}
              />,
              <DeleteOutlined 
                key="delete" 
                onClick={(e) => {
                  e.stopPropagation()
                  deleteCard(card.id)
                }}
              />
            ]}
          >
            <div className="mb-2">
              <h4 className="text-sm font-medium mb-1 line-clamp-2">{card.title}</h4>
              {card.description && (
                <p className="text-xs text-gray-600 line-clamp-3">{card.description}</p>
              )}
            </div>
            
            <div className="flex flex-wrap gap-1 mb-2">
              <Tag 
                color={PRIORITY_COLORS[card.priority]} 
                size="small"
                icon={<FlagOutlined />}
              >
                {PRIORITY_LABELS[card.priority]}
              </Tag>
              {card.tags.map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
            </div>

            <div className="flex justify-between items-center text-xs text-gray-500">
              {card.assignee && (
                <span>
                  <UserOutlined className="mr-1" />
                  {card.assignee}
                </span>
              )}
              {card.dueDate && (
                <span>
                  <CalendarOutlined className="mr-1" />
                  {new Date(card.dueDate).toLocaleDateString()}
                </span>
              )}
            </div>
          </Card>
        </div>
      )}
    </Draggable>
  ), [openCardModal, deleteCard])

  /**
   * 渲染列
   */
  const renderColumn = useCallback((column: KanbanColumn) => {
    const columnCards = kanbanData.cards.filter(card => card.status === column.status)
    
    return (
      <div key={column.id} className="flex-1 min-w-80 mx-2">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-gray-800">
              {column.title} ({columnCards.length})
            </h3>
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => openCardModal(column.status)}
            >
              添加卡片
            </Button>
          </div>

          <Droppable droppableId={column.status}>
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className={`min-h-96 ${
                  snapshot.isDraggingOver ? 'bg-blue-50' : ''
                } transition-colors`}
              >
                {columnCards.map((card, index) => renderCard(card, index))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </div>
      </div>
    )
  }, [kanbanData.cards, openCardModal, renderCard])

  return (
    <div className={`kanban-board ${className}`}>
      {/* 工具栏 */}
      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-xl font-semibold">看板管理</h2>
        <Button type="primary" onClick={saveKanban}>
          保存看板
        </Button>
      </div>

      {/* 看板内容 */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="flex overflow-x-auto pb-4">
          {kanbanData.columns.map(renderColumn)}
        </div>
      </DragDropContext>

      {/* 卡片编辑模态框 */}
      <Modal
        title={editingCard ? '编辑卡片' : '新建卡片'}
        open={isCardModalVisible}
        onOk={saveCard}
        onCancel={closeCardModal}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入卡片标题' }]}
          >
            <Input placeholder="请输入卡片标题" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input.TextArea 
              rows={3} 
              placeholder="请输入卡片描述" 
            />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="priority"
              label="优先级"
              initialValue={CardPriority.MEDIUM}
            >
              <Select>
                <Select.Option value={CardPriority.LOW}>
                  <Tag color={PRIORITY_COLORS[CardPriority.LOW]}>
                    {PRIORITY_LABELS[CardPriority.LOW]}
                  </Tag>
                </Select.Option>
                <Select.Option value={CardPriority.MEDIUM}>
                  <Tag color={PRIORITY_COLORS[CardPriority.MEDIUM]}>
                    {PRIORITY_LABELS[CardPriority.MEDIUM]}
                  </Tag>
                </Select.Option>
                <Select.Option value={CardPriority.HIGH}>
                  <Tag color={PRIORITY_COLORS[CardPriority.HIGH]}>
                    {PRIORITY_LABELS[CardPriority.HIGH]}
                  </Tag>
                </Select.Option>
                <Select.Option value={CardPriority.URGENT}>
                  <Tag color={PRIORITY_COLORS[CardPriority.URGENT]}>
                    {PRIORITY_LABELS[CardPriority.URGENT]}
                  </Tag>
                </Select.Option>
              </Select>
            </Form.Item>

            <Form.Item name="assignee" label="负责人">
              <Input placeholder="请输入负责人" />
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item name="tags" label="标签">
              <Select
                mode="tags"
                placeholder="请输入标签"
                tokenSeparators={[',']}
              />
            </Form.Item>

            <Form.Item name="dueDate" label="截止日期">
              <DatePicker 
                className="w-full"
                placeholder="请选择截止日期" 
              />
            </Form.Item>
          </div>
        </Form>
      </Modal>
    </div>
  )
}

export default KanbanBoard
