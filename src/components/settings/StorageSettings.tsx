/**
 * 存储设置组件
 * 提供用户配置数据存储方式的界面
 */

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Switch, 
  Select, 
  Button, 
  Alert, 
  Progress, 
  Descriptions, 
  Space, 
  Modal, 
  message,
  Tooltip,
  Typography
} from 'antd'
import { 
  DatabaseOutlined, 
  FolderOpenOutlined, 
  CloudDownloadOutlined, 
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { getStorageManager, StorageConfig } from '@services/storage/HybridStorageManager'
import { FileSystemProvider } from '@services/storage/providers/FileSystemProvider'
import { StorageStatus } from '@services/storage/HybridStorageManager'

const { Title, Text, Paragraph } = Typography
const { Option } = Select

/**
 * 存储设置组件
 */
const StorageSettings: React.FC = () => {
  const [config, setConfig] = useState<StorageConfig>()
  const [status, setStatus] = useState<StorageStatus>()
  const [loading, setLoading] = useState(false)
  const [fileSystemSupported] = useState(FileSystemProvider.isAvailable())
  const [showBackupModal, setShowBackupModal] = useState(false)

  // 加载存储配置和状态
  useEffect(() => {
    loadStorageInfo()
  }, [])

  /**
   * 加载存储信息
   */
  const loadStorageInfo = async () => {
    try {
      setLoading(true)
      const storageManager = getStorageManager()
      const storageStatus = await storageManager.getStorageStatus()
      setStatus(storageStatus)
      
      // 从localStorage加载配置
      const savedConfig = localStorage.getItem('storageConfig')
      if (savedConfig) {
        setConfig(JSON.parse(savedConfig))
      }
    } catch (error) {
      console.error('加载存储信息失败:', error)
      message.error('加载存储信息失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 更新存储配置
   */
  const updateConfig = async (newConfig: Partial<StorageConfig>) => {
    if (!config) return

    try {
      const updatedConfig = { ...config, ...newConfig }
      const storageManager = getStorageManager()
      await storageManager.updateConfig(updatedConfig)
      
      setConfig(updatedConfig)
      localStorage.setItem('storageConfig', JSON.stringify(updatedConfig))
      
      message.success('存储配置已更新')
      
      // 重新加载状态
      await loadStorageInfo()
    } catch (error) {
      console.error('更新存储配置失败:', error)
      message.error('更新存储配置失败')
    }
  }

  /**
   * 启用文件系统备份
   */
  const enableFileSystemBackup = async () => {
    try {
      setLoading(true)
      await updateConfig({ enableFileSystemBackup: true })
      message.success('文件系统备份已启用')
    } catch (error) {
      console.error('启用文件系统备份失败:', error)
      message.error('启用文件系统备份失败，请检查浏览器权限')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 执行手动备份
   */
  const performManualBackup = async () => {
    try {
      setLoading(true)
      const storageManager = getStorageManager()
      const result = await storageManager.performFullBackup()
      
      if (result.success) {
        message.success('备份完成')
        setShowBackupModal(false)
        await loadStorageInfo()
      } else {
        message.error('备份失败')
      }
    } catch (error) {
      console.error('手动备份失败:', error)
      message.error('备份失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 获取存储状态图标
   */
  const getStorageStatusIcon = (available: boolean) => {
    return available ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    )
  }

  if (!config || !status) {
    return <div>加载中...</div>
  }

  return (
    <div className="storage-settings">
      <Title level={3}>数据存储设置</Title>
      <Paragraph type="secondary">
        配置数据存储方式和备份策略，确保您的数据安全
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 存储状态概览 */}
        <Card title="存储状态" extra={<Button onClick={loadStorageInfo} loading={loading}>刷新</Button>}>
          <Descriptions column={2}>
            <Descriptions.Item 
              label="主存储" 
              span={1}
            >
              <Space>
                {getStorageStatusIcon(status.primaryStorageAvailable)}
                <Text>浏览器内置存储 (IndexedDB)</Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item 
              label="备份存储" 
              span={1}
            >
              <Space>
                {getStorageStatusIcon(status.backupStorageAvailable)}
                <Text>
                  {status.backupStorageAvailable ? '本地文件系统' : '未启用'}
                </Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="文档总数">
              {status.totalDocuments}
            </Descriptions.Item>
            <Descriptions.Item label="存储使用量">
              {formatFileSize(status.storageUsage.primary)}
            </Descriptions.Item>
            <Descriptions.Item label="最后备份时间" span={2}>
              {status.lastBackupTime ? 
                status.lastBackupTime.toLocaleString() : 
                '从未备份'
              }
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 主存储设置 */}
        <Card title={<><DatabaseOutlined /> 主存储设置</>}>
          <Alert
            message="当前使用浏览器内置存储 (IndexedDB)"
            description="数据存储在浏览器中，支持离线使用，无需额外权限。数据与浏览器绑定，清理浏览器数据时可能丢失。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Descriptions column={1}>
            <Descriptions.Item label="存储类型">
              浏览器内置存储 (IndexedDB)
            </Descriptions.Item>
            <Descriptions.Item label="兼容性">
              所有现代浏览器支持
            </Descriptions.Item>
            <Descriptions.Item label="数据位置">
              浏览器内部存储空间
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 备份存储设置 */}
        <Card title={<><FolderOpenOutlined /> 备份存储设置</>}>
          {fileSystemSupported ? (
            <div>
              <div style={{ marginBottom: 16 }}>
                <Space>
                  <Switch
                    checked={config.enableFileSystemBackup}
                    onChange={(checked) => updateConfig({ enableFileSystemBackup: checked })}
                    loading={loading}
                  />
                  <Text strong>启用本地文件备份</Text>
                  <Tooltip title="将数据备份到您选择的本地文件夹，防止数据丢失">
                    <ExclamationCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              </div>

              {config.enableFileSystemBackup ? (
                <Alert
                  message="文件系统备份已启用"
                  description="数据将自动备份到您选择的本地文件夹。首次启用时需要选择备份目录。"
                  type="success"
                  showIcon
                  action={
                    <Button size="small" onClick={enableFileSystemBackup}>
                      重新选择目录
                    </Button>
                  }
                />
              ) : (
                <Alert
                  message="建议启用文件系统备份"
                  description="启用后可以将数据备份到本地文件夹，即使浏览器数据被清理也不会丢失。"
                  type="warning"
                  showIcon
                  action={
                    <Button type="primary" size="small" onClick={enableFileSystemBackup}>
                      立即启用
                    </Button>
                  }
                />
              )}
            </div>
          ) : (
            <Alert
              message="您的浏览器不支持文件系统访问"
              description="建议使用 Chrome 86+ 或 Edge 86+ 浏览器以获得完整的备份功能。"
              type="warning"
              showIcon
            />
          )}
        </Card>

        {/* 自动备份设置 */}
        <Card title={<><CloudDownloadOutlined /> 自动备份设置</>}>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Switch
                checked={config.autoBackup}
                onChange={(checked) => updateConfig({ autoBackup: checked })}
                loading={loading}
              />
              <Text strong>启用自动备份</Text>
            </Space>
          </div>

          {config.autoBackup && (
            <div style={{ marginBottom: 16 }}>
              <Text>备份频率：</Text>
              <Select
                value={config.backupInterval}
                onChange={(value) => updateConfig({ backupInterval: value })}
                style={{ width: 120, marginLeft: 8 }}
              >
                <Option value={60000}>1分钟</Option>
                <Option value={300000}>5分钟</Option>
                <Option value={900000}>15分钟</Option>
                <Option value={1800000}>30分钟</Option>
                <Option value={3600000}>1小时</Option>
              </Select>
            </div>
          )}

          <Space>
            <Button 
              type="primary" 
              onClick={() => setShowBackupModal(true)}
              loading={loading}
            >
              立即备份
            </Button>
            <Button onClick={loadStorageInfo}>
              检查备份状态
            </Button>
          </Space>
        </Card>

        {/* 存储使用情况 */}
        <Card title="存储使用情况">
          <div style={{ marginBottom: 16 }}>
            <Text>主存储使用量：</Text>
            <Progress 
              percent={Math.min((status.storageUsage.primary / (50 * 1024 * 1024)) * 100, 100)} 
              format={() => formatFileSize(status.storageUsage.primary)}
            />
          </div>
          
          {status.storageUsage.backup && (
            <div>
              <Text>备份存储使用量：</Text>
              <Progress 
                percent={Math.min((status.storageUsage.backup / (100 * 1024 * 1024)) * 100, 100)} 
                format={() => formatFileSize(status.storageUsage.backup)}
              />
            </div>
          )}
        </Card>
      </Space>

      {/* 备份确认对话框 */}
      <Modal
        title="执行备份"
        open={showBackupModal}
        onOk={performManualBackup}
        onCancel={() => setShowBackupModal(false)}
        confirmLoading={loading}
      >
        <p>确定要立即执行完整备份吗？</p>
        <p>备份将包含所有文档和设置数据。</p>
      </Modal>
    </div>
  )
}

export default StorageSettings
