/**
 * 混合存储管理器
 * 支持IndexedDB主存储 + File System API备份存储的混合架构
 */

import { BaseDocument, DocumentFilter, BackupData, BackupResult, RestoreResult } from '@types/index'
import { IStorageProvider } from './interfaces/IStorageProvider'
import { IndexedDBProvider } from './providers/IndexedDBProvider'
import { FileSystemProvider } from './providers/FileSystemProvider'
import { ExportBackupProvider } from './providers/ExportBackupProvider'

/**
 * 存储配置接口
 */
export interface StorageConfig {
  primaryStorage: 'indexeddb' | 'filesystem'
  enableFileSystemBackup: boolean
  autoBackup: boolean
  backupInterval: number // 毫秒
  maxBackupFiles: number
}

/**
 * 存储状态接口
 */
export interface StorageStatus {
  primaryStorageAvailable: boolean
  backupStorageAvailable: boolean
  lastBackupTime?: Date
  totalDocuments: number
  storageUsage: {
    primary: number
    backup?: number
  }
}

/**
 * 混合存储管理器类
 * 实现多层次数据存储和备份机制
 */
export class HybridStorageManager {
  private primaryStorage: IStorageProvider
  private backupStorage?: IStorageProvider
  private exportBackup: ExportBackupProvider
  private config: StorageConfig
  private backupTimer?: NodeJS.Timeout

  constructor(config: StorageConfig) {
    this.config = config
    this.exportBackup = new ExportBackupProvider()
    
    // 初始化主存储
    this.initializePrimaryStorage()
    
    // 初始化备份存储
    this.initializeBackupStorage()
    
    // 启动自动备份
    if (this.config.autoBackup) {
      this.startAutoBackup()
    }
  }

  /**
   * 初始化主存储
   */
  private initializePrimaryStorage(): void {
    switch (this.config.primaryStorage) {
      case 'filesystem':
        if (FileSystemProvider.isAvailable()) {
          this.primaryStorage = new FileSystemProvider()
        } else {
          console.warn('File System API不可用，降级到IndexedDB')
          this.primaryStorage = new IndexedDBProvider()
        }
        break
      case 'indexeddb':
      default:
        this.primaryStorage = new IndexedDBProvider()
        break
    }
  }

  /**
   * 初始化备份存储
   */
  private initializeBackupStorage(): void {
    if (this.config.enableFileSystemBackup && FileSystemProvider.isAvailable()) {
      this.backupStorage = new FileSystemProvider()
    }
  }

  /**
   * 保存文档
   * @param document 要保存的文档
   */
  async save(document: BaseDocument): Promise<void> {
    try {
      // 保存到主存储
      await this.primaryStorage.save(document)
      console.log(`文档 ${document.id} 已保存到主存储`)

      // 自动备份到辅助存储
      if (this.backupStorage && this.config.autoBackup) {
        try {
          await this.backupStorage.save(document)
          console.log(`文档 ${document.id} 已备份到辅助存储`)
        } catch (error) {
          console.warn('备份存储失败:', error)
          // 备份失败不影响主要功能
        }
      }

    } catch (error) {
      console.error('保存文档失败:', error)
      throw new Error(`保存文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 加载文档
   * @param id 文档ID
   * @returns 文档对象或null
   */
  async load(id: string): Promise<BaseDocument | null> {
    try {
      // 优先从主存储加载
      let document = await this.primaryStorage.load(id)
      
      if (document) {
        return document
      }

      // 如果主存储失败，尝试从备份存储恢复
      if (this.backupStorage) {
        console.log(`从主存储未找到文档 ${id}，尝试从备份存储恢复`)
        document = await this.backupStorage.load(id)
        
        if (document) {
          console.log(`从备份存储恢复文档 ${id}`)
          // 恢复到主存储
          try {
            await this.primaryStorage.save(document)
            console.log(`文档 ${id} 已恢复到主存储`)
          } catch (error) {
            console.warn('恢复到主存储失败:', error)
          }
          return document
        }
      }

      return null
    } catch (error) {
      console.error('加载文档失败:', error)
      throw new Error(`加载文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除文档
   * @param id 文档ID
   */
  async delete(id: string): Promise<void> {
    try {
      // 从主存储删除
      await this.primaryStorage.delete(id)
      
      // 从备份存储删除
      if (this.backupStorage) {
        try {
          await this.backupStorage.delete(id)
        } catch (error) {
          console.warn('从备份存储删除失败:', error)
        }
      }
      
      console.log(`文档 ${id} 已删除`)
    } catch (error) {
      console.error('删除文档失败:', error)
      throw new Error(`删除文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 列出文档
   * @param filter 过滤条件
   * @returns 文档列表
   */
  async list(filter?: DocumentFilter): Promise<BaseDocument[]> {
    try {
      return await this.primaryStorage.list(filter)
    } catch (error) {
      console.error('列出文档失败:', error)
      
      // 如果主存储失败，尝试从备份存储获取
      if (this.backupStorage) {
        try {
          console.log('主存储失败，尝试从备份存储获取文档列表')
          return await this.backupStorage.list(filter)
        } catch (backupError) {
          console.error('备份存储也失败:', backupError)
        }
      }
      
      throw new Error(`列出文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 执行完整备份
   * @returns 备份结果
   */
  async performFullBackup(): Promise<BackupResult> {
    try {
      console.log('开始执行完整备份...')
      
      // 从主存储获取所有数据
      const backupResult = await this.primaryStorage.backup()
      
      // 如果有备份存储，也执行备份
      if (this.backupStorage) {
        try {
          await this.backupStorage.restore(backupResult.data)
        } catch (error) {
          console.warn('备份到辅助存储失败:', error)
        }
      }
      
      // 导出备份文件
      await this.exportBackup.createBackupFile(backupResult.data)
      
      console.log('完整备份执行成功')
      return backupResult
    } catch (error) {
      console.error('执行完整备份失败:', error)
      throw new Error(`执行完整备份失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 恢复数据
   * @param backupData 备份数据
   * @returns 恢复结果
   */
  async restore(backupData: BackupData): Promise<RestoreResult> {
    try {
      console.log('开始恢复数据...')
      
      // 恢复到主存储
      const restoreResult = await this.primaryStorage.restore(backupData)
      
      // 如果有备份存储，也恢复
      if (this.backupStorage) {
        try {
          await this.backupStorage.restore(backupData)
        } catch (error) {
          console.warn('恢复到备份存储失败:', error)
        }
      }
      
      console.log('数据恢复成功')
      return restoreResult
    } catch (error) {
      console.error('恢复数据失败:', error)
      throw new Error(`恢复数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取存储状态
   * @returns 存储状态信息
   */
  async getStorageStatus(): Promise<StorageStatus> {
    try {
      const documents = await this.list()
      
      return {
        primaryStorageAvailable: this.primaryStorage.isAvailable(),
        backupStorageAvailable: this.backupStorage?.isAvailable() || false,
        lastBackupTime: this.getLastBackupTime(),
        totalDocuments: documents.length,
        storageUsage: {
          primary: await this.calculateStorageUsage(documents),
          backup: this.backupStorage ? await this.calculateStorageUsage(documents) : undefined
        }
      }
    } catch (error) {
      console.error('获取存储状态失败:', error)
      throw new Error(`获取存储状态失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 更新存储配置
   * @param newConfig 新的配置
   */
  async updateConfig(newConfig: Partial<StorageConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig }
    
    // 重新初始化备份存储
    if (newConfig.enableFileSystemBackup !== undefined) {
      this.initializeBackupStorage()
    }
    
    // 重新启动自动备份
    if (newConfig.autoBackup !== undefined || newConfig.backupInterval !== undefined) {
      this.stopAutoBackup()
      if (this.config.autoBackup) {
        this.startAutoBackup()
      }
    }
    
    console.log('存储配置已更新:', this.config)
  }

  /**
   * 启动自动备份
   */
  private startAutoBackup(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer)
    }
    
    this.backupTimer = setInterval(() => {
      this.performFullBackup().catch(error => {
        console.error('自动备份失败:', error)
      })
    }, this.config.backupInterval)
    
    console.log(`自动备份已启动，间隔: ${this.config.backupInterval}ms`)
  }

  /**
   * 停止自动备份
   */
  private stopAutoBackup(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer)
      this.backupTimer = undefined
      console.log('自动备份已停止')
    }
  }

  /**
   * 获取最后备份时间
   */
  private getLastBackupTime(): Date | undefined {
    // 从localStorage获取最后备份时间
    const lastBackupTime = localStorage.getItem('lastBackupTime')
    return lastBackupTime ? new Date(lastBackupTime) : undefined
  }

  /**
   * 计算存储使用量
   */
  private async calculateStorageUsage(documents: BaseDocument[]): Promise<number> {
    return documents.reduce((total, doc) => {
      return total + JSON.stringify(doc).length
    }, 0)
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopAutoBackup()
    console.log('存储管理器已销毁')
  }
}

/**
 * 创建默认存储配置
 */
export function createDefaultStorageConfig(): StorageConfig {
  return {
    primaryStorage: 'indexeddb',
    enableFileSystemBackup: FileSystemProvider.isAvailable(),
    autoBackup: true,
    backupInterval: 5 * 60 * 1000, // 5分钟
    maxBackupFiles: 10
  }
}

/**
 * 存储管理器单例
 */
let storageManagerInstance: HybridStorageManager | null = null

/**
 * 获取存储管理器实例
 */
export function getStorageManager(): HybridStorageManager {
  if (!storageManagerInstance) {
    const config = createDefaultStorageConfig()
    storageManagerInstance = new HybridStorageManager(config)
  }
  return storageManagerInstance
}
