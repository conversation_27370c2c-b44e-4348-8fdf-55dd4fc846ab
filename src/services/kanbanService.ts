/**
 * 看板服务
 * 提供看板文档的创建、保存、加载等功能
 */

import { documentDAO } from './database/documentDAO'
import { generateUUID } from '@utils/index'
import { 
  KanbanDocument, 
  KanbanContent,
  DocumentType, 
  BaseDocument 
} from '@types/index'
import { KanbanData, KanbanCard, KanbanColumn, CardStatus, CardPriority } from '@components/kanban/KanbanBoard'

/**
 * 看板服务类
 */
export class KanbanService {
  /**
   * 创建新的看板文档
   */
  async createKanban(title: string = '新建看板'): Promise<KanbanDocument> {
    const now = new Date()
    
    // 创建默认的看板内容
    const kanbanContent: KanbanContent = {
      columns: [
        {
          id: generateUUID(),
          title: '待办',
          status: CardStatus.TODO,
          cards: [],
          color: '#f0f0f0',
          order: 0
        },
        {
          id: generateUUID(),
          title: '进行中',
          status: CardStatus.IN_PROGRESS,
          cards: [],
          color: '#e6f7ff',
          order: 1
        },
        {
          id: generateUUID(),
          title: '待审核',
          status: CardStatus.REVIEW,
          cards: [],
          color: '#fff7e6',
          order: 2
        },
        {
          id: generateUUID(),
          title: '已完成',
          status: CardStatus.DONE,
          cards: [],
          color: '#f6ffed',
          order: 3
        }
      ],
      cards: [],
      settings: {
        allowAddColumns: true,
        allowDeleteColumns: false,
        cardLimit: 50,
        columnLimit: 10
      }
    }

    const kanbanDoc: KanbanDocument = {
      id: generateUUID(),
      type: DocumentType.KANBAN,
      title,
      content: kanbanContent,
      createdAt: now,
      updatedAt: now,
      tags: [],
      metadata: {
        version: '1.0.0',
        cardCount: 0,
        columnCount: 4
      }
    }

    // 转换为基础文档格式保存到数据库
    const baseDoc: BaseDocument = {
      id: kanbanDoc.id,
      type: DocumentType.KANBAN,
      title: kanbanDoc.title,
      content: kanbanDoc.content,
      createdAt: kanbanDoc.createdAt,
      updatedAt: kanbanDoc.updatedAt,
      tags: kanbanDoc.tags,
      metadata: kanbanDoc.metadata
    }

    await documentDAO.create(baseDoc)
    console.log('看板文档创建成功:', kanbanDoc.title)
    
    return kanbanDoc
  }

  /**
   * 保存看板文档
   */
  async saveKanban(kanban: KanbanDocument): Promise<void> {
    const updatedKanban = {
      ...kanban,
      updatedAt: new Date(),
      metadata: {
        ...kanban.metadata,
        cardCount: kanban.content.cards.length,
        columnCount: kanban.content.columns.length
      }
    }

    const baseDoc: BaseDocument = {
      id: updatedKanban.id,
      type: DocumentType.KANBAN,
      title: updatedKanban.title,
      content: updatedKanban.content,
      createdAt: updatedKanban.createdAt,
      updatedAt: updatedKanban.updatedAt,
      tags: updatedKanban.tags,
      metadata: updatedKanban.metadata
    }

    await documentDAO.update(baseDoc)
    console.log('看板文档保存成功:', updatedKanban.title)
  }

  /**
   * 加载看板文档
   */
  async loadKanban(id: string): Promise<KanbanDocument | null> {
    try {
      const baseDoc = await documentDAO.getById(id)
      
      if (!baseDoc || baseDoc.type !== DocumentType.KANBAN) {
        console.warn('看板文档不存在或类型不匹配:', id)
        return null
      }

      const kanbanDoc: KanbanDocument = {
        id: baseDoc.id,
        type: DocumentType.KANBAN,
        title: baseDoc.title,
        content: baseDoc.content as KanbanContent,
        createdAt: baseDoc.createdAt,
        updatedAt: baseDoc.updatedAt,
        tags: baseDoc.tags,
        metadata: {
          version: baseDoc.metadata?.version || '1.0.0',
          cardCount: baseDoc.metadata?.cardCount || 0,
          columnCount: baseDoc.metadata?.columnCount || 0,
          thumbnail: baseDoc.metadata?.thumbnail
        }
      }

      console.log('看板文档加载成功:', kanbanDoc.title)
      return kanbanDoc
    } catch (error) {
      console.error('加载看板文档失败:', error)
      return null
    }
  }

  /**
   * 删除看板文档
   */
  async deleteKanban(id: string): Promise<boolean> {
    try {
      await documentDAO.delete(id)
      console.log('看板文档删除成功:', id)
      return true
    } catch (error) {
      console.error('删除看板文档失败:', error)
      return false
    }
  }

  /**
   * 获取所有看板文档列表
   */
  async getKanbanList(): Promise<KanbanDocument[]> {
    try {
      const baseDocs = await documentDAO.getByType(DocumentType.KANBAN)
      
      return baseDocs.map(baseDoc => ({
        id: baseDoc.id,
        type: DocumentType.KANBAN,
        title: baseDoc.title,
        content: baseDoc.content as KanbanContent,
        createdAt: baseDoc.createdAt,
        updatedAt: baseDoc.updatedAt,
        tags: baseDoc.tags,
        metadata: {
          version: baseDoc.metadata?.version || '1.0.0',
          cardCount: baseDoc.metadata?.cardCount || 0,
          columnCount: baseDoc.metadata?.columnCount || 0,
          thumbnail: baseDoc.metadata?.thumbnail
        }
      }))
    } catch (error) {
      console.error('获取看板文档列表失败:', error)
      return []
    }
  }

  /**
   * 复制看板文档
   */
  async duplicateKanban(id: string, newTitle?: string): Promise<KanbanDocument | null> {
    try {
      const originalKanban = await this.loadKanban(id)
      if (!originalKanban) {
        return null
      }

      const duplicatedKanban = await this.createKanban(
        newTitle || `${originalKanban.title} - 副本`
      )

      // 复制内容
      duplicatedKanban.content = {
        ...originalKanban.content,
        columns: originalKanban.content.columns.map(column => ({
          ...column,
          id: generateUUID() // 生成新的ID
        })),
        cards: originalKanban.content.cards.map(card => ({
          ...card,
          id: generateUUID() // 生成新的ID
        }))
      }

      duplicatedKanban.tags = [...originalKanban.tags]

      await this.saveKanban(duplicatedKanban)
      console.log('看板文档复制成功')
      
      return duplicatedKanban
    } catch (error) {
      console.error('复制看板文档失败:', error)
      return null
    }
  }

  /**
   * 更新看板数据
   */
  async updateKanbanData(id: string, data: KanbanData): Promise<boolean> {
    try {
      const kanban = await this.loadKanban(id)
      if (!kanban) return false

      kanban.content.cards = data.cards
      kanban.content.columns = data.columns
      
      await this.saveKanban(kanban)
      return true
    } catch (error) {
      console.error('更新看板数据失败:', error)
      return false
    }
  }

  /**
   * 搜索看板文档
   */
  async searchKanbans(query: string): Promise<KanbanDocument[]> {
    try {
      const allKanbans = await this.getKanbanList()
      
      if (!query.trim()) {
        return allKanbans
      }

      const searchTerm = query.toLowerCase()
      
      return allKanbans.filter(kanban => 
        kanban.title.toLowerCase().includes(searchTerm) ||
        kanban.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
        kanban.content.cards.some(card => 
          card.title.toLowerCase().includes(searchTerm) ||
          card.description.toLowerCase().includes(searchTerm) ||
          card.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        )
      )
    } catch (error) {
      console.error('搜索看板文档失败:', error)
      return []
    }
  }

  /**
   * 获取看板统计信息
   */
  async getKanbanStats(id: string): Promise<{
    totalCards: number
    cardsByStatus: Record<CardStatus, number>
    cardsByPriority: Record<CardPriority, number>
    overdueTasks: number
  } | null> {
    try {
      const kanban = await this.loadKanban(id)
      if (!kanban) return null

      const cards = kanban.content.cards
      const now = new Date()

      const stats = {
        totalCards: cards.length,
        cardsByStatus: {
          [CardStatus.TODO]: 0,
          [CardStatus.IN_PROGRESS]: 0,
          [CardStatus.REVIEW]: 0,
          [CardStatus.DONE]: 0
        },
        cardsByPriority: {
          [CardPriority.LOW]: 0,
          [CardPriority.MEDIUM]: 0,
          [CardPriority.HIGH]: 0,
          [CardPriority.URGENT]: 0
        },
        overdueTasks: 0
      }

      cards.forEach(card => {
        // 统计状态
        stats.cardsByStatus[card.status]++
        
        // 统计优先级
        stats.cardsByPriority[card.priority]++
        
        // 统计逾期任务
        if (card.dueDate && new Date(card.dueDate) < now && card.status !== CardStatus.DONE) {
          stats.overdueTasks++
        }
      })

      return stats
    } catch (error) {
      console.error('获取看板统计信息失败:', error)
      return null
    }
  }

  /**
   * 导出看板数据
   */
  async exportKanbanData(id: string): Promise<string | null> {
    try {
      const kanban = await this.loadKanban(id)
      if (!kanban) return null

      const exportData = {
        title: kanban.title,
        createdAt: kanban.createdAt,
        updatedAt: kanban.updatedAt,
        columns: kanban.content.columns,
        cards: kanban.content.cards
      }

      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出看板数据失败:', error)
      return null
    }
  }

  /**
   * 导入看板数据
   */
  async importKanbanData(jsonData: string, title?: string): Promise<KanbanDocument | null> {
    try {
      const importData = JSON.parse(jsonData)
      
      const kanban = await this.createKanban(title || importData.title || '导入的看板')
      
      kanban.content.columns = importData.columns || kanban.content.columns
      kanban.content.cards = importData.cards || []
      
      await this.saveKanban(kanban)
      console.log('看板数据导入成功')
      
      return kanban
    } catch (error) {
      console.error('导入看板数据失败:', error)
      return null
    }
  }
}

// 导出单例实例
export const kanbanService = new KanbanService()
