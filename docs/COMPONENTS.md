# 组件使用指南

本文档详细介绍了多维度笔记应用中各个组件的使用方法和最佳实践。

## 白板组件

### WhiteboardCanvas 白板画布

白板画布组件提供了完整的绘图功能，支持多种绘图工具和交互操作。

#### 基本使用

```tsx
import WhiteboardCanvas from '@components/whiteboard/WhiteboardCanvas'

function MyWhiteboardPage() {
  const handleSave = (imageData: string) => {
    console.log('保存白板图片:', imageData)
    // 处理保存逻辑
  }

  return (
    <WhiteboardCanvas
      width={1200}
      height={800}
      onSave={handleSave}
      className="my-whiteboard"
    />
  )
}
```

#### 功能特性

1. **绘图工具**
   - 画笔：自由绘制
   - 矩形：绘制矩形框
   - 圆形：绘制圆形
   - 直线：绘制直线

2. **样式控制**
   - 颜色选择器：支持自定义颜色
   - 线条粗细：1-20像素可调
   - 实时预览：绘制过程中实时显示

3. **操作功能**
   - 撤销/重做：支持多步操作历史
   - 清空画布：一键清除所有内容
   - 保存导出：生成PNG格式图片

#### 使用技巧

- 使用合适的画布尺寸以适应不同设备
- 定期保存避免数据丢失
- 利用撤销功能进行快速修正

## 思维导图组件

### MindMapCanvas 思维导图画布

思维导图组件支持创建和编辑层级化的思维导图结构。

#### 基本使用

```tsx
import MindMapCanvas from '@components/mindmap/MindMapCanvas'

function MyMindMapPage() {
  const handleSave = (data: MindMapData) => {
    console.log('保存思维导图:', data)
    // 处理保存逻辑
  }

  return (
    <MindMapCanvas
      width={1200}
      height={800}
      onSave={handleSave}
    />
  )
}
```

#### 交互操作

1. **节点操作**
   - 单击选择节点
   - 双击编辑节点文本
   - 右键显示上下文菜单

2. **结构管理**
   - 添加子节点：选中父节点后点击"添加节点"
   - 删除节点：选中节点后点击删除按钮
   - 折叠展开：点击节点右上角的+/-按钮

3. **视觉效果**
   - 自动连接线：父子节点间自动绘制连接线
   - 层级颜色：不同层级使用不同颜色区分
   - 选中高亮：当前选中节点高亮显示

#### 最佳实践

- 保持节点文本简洁明了
- 合理使用层级结构，避免过深嵌套
- 定期保存思维导图内容

## 看板组件

### KanbanBoard 看板面板

看板组件提供了完整的任务管理功能，支持拖拽操作和多维度任务属性。

#### 基本使用

```tsx
import KanbanBoard from '@components/kanban/KanbanBoard'

function MyKanbanPage() {
  const kanbanData = {
    columns: [
      { id: 'todo', title: '待办', status: CardStatus.TODO, cards: [], color: '#f0f0f0' },
      { id: 'doing', title: '进行中', status: CardStatus.IN_PROGRESS, cards: [], color: '#e6f7ff' },
      { id: 'review', title: '待审核', status: CardStatus.REVIEW, cards: [], color: '#fff7e6' },
      { id: 'done', title: '已完成', status: CardStatus.DONE, cards: [], color: '#f6ffed' }
    ],
    cards: []
  }

  const handleSave = (data: KanbanData) => {
    console.log('保存看板数据:', data)
    // 处理保存逻辑
  }

  return (
    <KanbanBoard
      data={kanbanData}
      onSave={handleSave}
    />
  )
}
```

#### 卡片属性

1. **基本信息**
   - 标题：任务的简短描述
   - 描述：详细的任务说明
   - 状态：待办、进行中、待审核、已完成

2. **扩展属性**
   - 优先级：低、中、高、紧急
   - 标签：自定义分类标签
   - 负责人：任务责任人
   - 截止日期：任务完成期限

3. **视觉标识**
   - 优先级颜色：不同优先级使用不同颜色
   - 逾期提醒：超过截止日期的任务特殊标识
   - 标签展示：以标签形式显示分类信息

#### 操作指南

1. **创建卡片**
   - 点击列标题旁的"+"按钮
   - 填写卡片信息
   - 选择优先级和标签

2. **编辑卡片**
   - 点击卡片上的编辑按钮
   - 修改卡片属性
   - 保存更改

3. **拖拽操作**
   - 拖拽卡片到不同列改变状态
   - 在同一列内拖拽调整顺序
   - 拖拽过程中显示视觉反馈

## 图谱组件

### GraphVisualization 关系图谱

关系图谱组件用于可视化文档间的关联关系，提供交互式的网络图展示。

#### 基本使用

```tsx
import GraphVisualization from '@components/graph/GraphVisualization'

function MyGraphPage() {
  const handleNodeClick = (document: BaseDocument) => {
    console.log('点击节点:', document.title)
  }

  const handleNodeDoubleClick = (document: BaseDocument) => {
    console.log('双击节点，跳转到:', document.title)
    // 导航到对应文档
  }

  return (
    <GraphVisualization
      documents={documents}
      links={links}
      onNodeClick={handleNodeClick}
      onNodeDoubleClick={handleNodeDoubleClick}
    />
  )
}
```

#### 交互功能

1. **视图控制**
   - 缩放：鼠标滚轮或缩放按钮
   - 平移：拖拽空白区域
   - 重置：恢复默认视图

2. **节点交互**
   - 悬停：显示节点详细信息
   - 单击：选中节点并高亮相关连接
   - 双击：跳转到对应文档

3. **过滤功能**
   - 文档类型过滤：只显示特定类型的文档
   - 链接数量过滤：隐藏链接较少的节点
   - 标签显示控制：开关节点标签显示

#### 视觉设计

- **节点样式**：不同文档类型使用不同颜色和图标
- **连接线样式**：不同链接类型使用不同颜色和粗细
- **布局算法**：自动计算节点位置，避免重叠

## 链接管理组件

### LinkManager 链接管理器

链接管理器提供了创建、查看和管理文档间关联关系的界面。

#### 基本使用

```tsx
import LinkManager from '@components/link/LinkManager'

function MyDocumentPage() {
  const [showLinkManager, setShowLinkManager] = useState(false)
  const currentDocumentId = 'doc-123'

  const handleLinkCreated = (link: DocumentLink) => {
    console.log('创建链接:', link)
    // 更新文档关联状态
  }

  const handleLinkDeleted = (linkId: string) => {
    console.log('删除链接:', linkId)
    // 更新文档关联状态
  }

  return (
    <>
      <Button onClick={() => setShowLinkManager(true)}>
        管理链接
      </Button>
      
      <LinkManager
        visible={showLinkManager}
        currentDocumentId={currentDocumentId}
        onClose={() => setShowLinkManager(false)}
        onLinkCreated={handleLinkCreated}
        onLinkDeleted={handleLinkDeleted}
      />
    </>
  )
}
```

#### 功能模块

1. **现有链接**
   - 显示当前文档的所有关联
   - 区分出链和入链
   - 支持删除不需要的链接

2. **创建链接**
   - 搜索目标文档
   - 选择链接类型
   - 创建双向关联

3. **智能推荐**
   - 基于内容相似性推荐
   - 标签匹配推荐
   - 时间相近性推荐

#### 链接类型

- **引用 (Reference)**：一个文档引用另一个文档的内容
- **相关 (Related)**：两个文档内容相关但无直接引用关系
- **父子 (Parent-Child)**：层级关系，如章节和子章节
- **依赖 (Dependency)**：一个文档依赖另一个文档的信息

## 通用组件

### 响应式设计

所有组件都支持响应式设计，能够适应不同屏幕尺寸：

- **桌面端**：完整功能，大屏幕优化
- **平板端**：适中的界面元素，触摸友好
- **移动端**：简化界面，核心功能保留

### 主题支持

组件支持明暗主题切换：

```tsx
// 在应用根组件中设置主题
<ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
  <App />
</ConfigProvider>
```

### 国际化

组件文本支持中英文切换：

```tsx
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'

<ConfigProvider locale={zhCN}>
  <App />
</ConfigProvider>
```

## 性能优化建议

1. **大数据处理**
   - 使用虚拟滚动处理大量节点
   - 实现懒加载减少初始渲染时间
   - 使用防抖优化搜索和过滤操作

2. **内存管理**
   - 及时清理事件监听器
   - 使用React.memo优化重渲染
   - 合理使用useCallback和useMemo

3. **用户体验**
   - 添加加载状态提示
   - 实现乐观更新
   - 提供操作反馈和错误提示

## 自定义扩展

所有组件都支持通过props进行自定义：

- **样式定制**：通过className和style属性
- **行为定制**：通过回调函数控制组件行为
- **内容定制**：通过render props模式自定义渲染内容

这种设计使得组件既易于使用，又具有良好的扩展性。
