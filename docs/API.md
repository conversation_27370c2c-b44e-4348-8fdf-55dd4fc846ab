# API 文档

本文档描述了多维度笔记应用的主要服务接口和组件API。

## 服务层 API

### 白板服务 (WhiteboardService)

白板服务提供白板文档的完整生命周期管理。

#### 创建白板
```typescript
async createWhiteboard(title?: string): Promise<WhiteboardDocument>
```
- **参数**: 
  - `title` (可选): 白板标题，默认为"新建白板"
- **返回**: 新创建的白板文档对象
- **功能**: 创建包含默认画布设置的新白板文档

#### 保存白板
```typescript
async saveWhiteboard(whiteboard: WhiteboardDocument): Promise<void>
```
- **参数**: 
  - `whiteboard`: 要保存的白板文档对象
- **功能**: 保存白板文档并更新元数据（如元素数量）

#### 加载白板
```typescript
async loadWhiteboard(id: string): Promise<WhiteboardDocument | null>
```
- **参数**: 
  - `id`: 白板文档ID
- **返回**: 白板文档对象，如果不存在则返回null
- **功能**: 从数据库加载指定的白板文档

#### 删除白板
```typescript
async deleteWhiteboard(id: string): Promise<boolean>
```
- **参数**: 
  - `id`: 要删除的白板文档ID
- **返回**: 删除成功返回true，失败返回false

#### 获取白板列表
```typescript
async getWhiteboardList(): Promise<WhiteboardDocument[]>
```
- **返回**: 所有白板文档的数组
- **功能**: 获取用户的所有白板文档

#### 搜索白板
```typescript
async searchWhiteboards(query: string): Promise<WhiteboardDocument[]>
```
- **参数**: 
  - `query`: 搜索关键词
- **返回**: 匹配的白板文档数组
- **功能**: 根据标题和标签搜索白板文档

#### 导出白板
```typescript
async exportWhiteboardAsImage(
  whiteboard: WhiteboardDocument, 
  options?: { format: 'png' | 'jpg'; quality?: number }
): Promise<string | null>
```
- **参数**: 
  - `whiteboard`: 要导出的白板文档
  - `options`: 导出选项（格式、质量等）
- **返回**: Base64编码的图片数据，失败返回null

### 思维导图服务 (MindMapService)

思维导图服务管理思维导图文档的创建、编辑和存储。

#### 创建思维导图
```typescript
async createMindMap(title?: string): Promise<MindMapDocument>
```
- **参数**: 
  - `title` (可选): 思维导图标题，默认为"新建思维导图"
- **返回**: 包含根节点的新思维导图文档
- **功能**: 创建带有默认中心主题节点的思维导图

#### 更新思维导图数据
```typescript
async updateMindMapData(id: string, data: MindMapData): Promise<boolean>
```
- **参数**: 
  - `id`: 思维导图文档ID
  - `data`: 包含节点和连接线的数据对象
- **返回**: 更新成功返回true
- **功能**: 更新思维导图的节点和连接线数据

#### 导出思维导图
```typescript
async exportMindMapAsImage(
  mindmap: MindMapDocument, 
  options?: { format: 'png' | 'jpg'; quality?: number }
): Promise<string | null>
```
- **功能**: 将思维导图渲染为图片并返回Base64数据

### 看板服务 (KanbanService)

看板服务提供任务看板的管理功能。

#### 创建看板
```typescript
async createKanban(title?: string): Promise<KanbanDocument>
```
- **功能**: 创建包含默认四列（待办、进行中、待审核、已完成）的看板

#### 获取看板统计
```typescript
async getKanbanStats(id: string): Promise<{
  totalCards: number
  cardsByStatus: Record<CardStatus, number>
  cardsByPriority: Record<CardPriority, number>
  overdueTasks: number
} | null>
```
- **返回**: 看板的详细统计信息
- **功能**: 计算卡片数量、状态分布、优先级分布和逾期任务

#### 导出看板数据
```typescript
async exportKanbanData(id: string): Promise<string | null>
```
- **返回**: JSON格式的看板数据
- **功能**: 导出看板的完整数据用于备份或迁移

#### 导入看板数据
```typescript
async importKanbanData(jsonData: string, title?: string): Promise<KanbanDocument | null>
```
- **参数**: 
  - `jsonData`: JSON格式的看板数据
  - `title`: 新看板的标题
- **功能**: 从JSON数据创建新的看板文档

### 链接服务 (LinkService)

链接服务管理文档间的关联关系。

#### 创建双向链接
```typescript
async createBidirectionalLink(
  sourceId: string, 
  targetId: string, 
  type: LinkType
): Promise<DocumentLink>
```
- **参数**: 
  - `sourceId`: 源文档ID
  - `targetId`: 目标文档ID
  - `type`: 链接类型（引用、相关、父子、依赖）
- **功能**: 创建两个文档间的双向关联

#### 获取关联文档
```typescript
async getLinkedDocuments(documentId: string): Promise<{
  outgoing: Array<{ document: BaseDocument; link: DocumentLink }>
  incoming: Array<{ document: BaseDocument; link: DocumentLink }>
}>
```
- **返回**: 文档的出链和入链信息
- **功能**: 获取与指定文档关联的所有文档

#### 获取链接建议
```typescript
async getLinkSuggestions(documentId: string, limit?: number): Promise<LinkSuggestion[]>
```
- **参数**: 
  - `documentId`: 当前文档ID
  - `limit`: 建议数量限制
- **返回**: 智能推荐的链接建议列表
- **功能**: 基于内容相似性、标签匹配等因素推荐相关文档

#### 获取链接统计
```typescript
async getLinkStats(): Promise<{
  totalLinks: number
  linksByType: Record<LinkType, number>
  orphanDocuments: BaseDocument[]
  mostLinkedDocuments: Array<{ document: BaseDocument; linkCount: number }>
}>
```
- **返回**: 链接系统的统计信息
- **功能**: 分析链接分布、孤立文档、热门文档等

## 组件 API

### 白板画布组件 (WhiteboardCanvas)

```typescript
interface WhiteboardCanvasProps {
  width?: number              // 画布宽度，默认800
  height?: number             // 画布高度，默认600
  onSave?: (imageData: string) => void  // 保存回调
  className?: string          // 自定义CSS类名
}
```

#### 功能特性
- 支持画笔、矩形、圆形、直线绘制
- 颜色选择和线条粗细调节
- 撤销/重做操作
- 清空画布功能
- 图片导出

### 思维导图画布组件 (MindMapCanvas)

```typescript
interface MindMapCanvasProps {
  width?: number              // 画布宽度，默认1200
  height?: number             // 画布高度，默认800
  onSave?: (data: MindMapData) => void  // 保存回调
  className?: string          // 自定义CSS类名
}
```

#### 功能特性
- 节点创建、编辑、删除
- 父子关系管理
- 节点折叠/展开
- 双击编辑节点文本
- 自动连接线绘制

### 看板组件 (KanbanBoard)

```typescript
interface KanbanBoardProps {
  data?: KanbanData           // 看板数据
  onSave?: (data: KanbanData) => void  // 保存回调
  className?: string          // 自定义CSS类名
}
```

#### 功能特性
- 拖拽操作支持
- 卡片创建、编辑、删除
- 优先级和标签管理
- 截止日期设置
- 多列布局

### 关系图谱组件 (GraphVisualization)

```typescript
interface GraphVisualizationProps {
  documents: BaseDocument[]   // 文档数据
  links: DocumentLink[]       // 链接数据
  onNodeClick?: (document: BaseDocument) => void      // 节点点击回调
  onNodeDoubleClick?: (document: BaseDocument) => void // 节点双击回调
  className?: string          // 自定义CSS类名
}
```

#### 功能特性
- 交互式图谱可视化
- 缩放、平移操作
- 节点和边的样式定制
- 类型过滤和搜索
- 统计信息展示

### 链接管理器组件 (LinkManager)

```typescript
interface LinkManagerProps {
  visible: boolean            // 是否显示
  currentDocumentId: string   // 当前文档ID
  onClose: () => void         // 关闭回调
  onLinkCreated?: (link: DocumentLink) => void    // 链接创建回调
  onLinkDeleted?: (linkId: string) => void        // 链接删除回调
}
```

#### 功能特性
- 现有链接查看和管理
- 文档搜索和链接创建
- 智能链接推荐
- 多种链接类型支持

## 数据类型

### 文档类型
```typescript
enum DocumentType {
  TEXT = 'text',
  WHITEBOARD = 'whiteboard', 
  MINDMAP = 'mindmap',
  KANBAN = 'kanban'
}
```

### 链接类型
```typescript
enum LinkType {
  REFERENCE = 'reference',    // 引用
  RELATED = 'related',        // 相关
  PARENT_CHILD = 'parent_child', // 父子
  DEPENDENCY = 'dependency'   // 依赖
}
```

### 基础文档接口
```typescript
interface BaseDocument {
  id: string
  type: DocumentType
  title: string
  content: any
  createdAt: Date
  updatedAt: Date
  tags: string[]
  metadata?: Record<string, any>
}
```

### 文档链接接口
```typescript
interface DocumentLink {
  id: string
  sourceId: string
  targetId: string
  type: LinkType
  label?: string
  createdAt: Date
  metadata?: Record<string, any>
}
```

## 错误处理

所有服务方法都包含适当的错误处理：

- 数据库操作失败时返回null或false
- 控制台输出详细的错误日志
- 用户界面显示友好的错误提示
- 网络错误和数据格式错误的处理

## 性能优化

- 使用IndexedDB进行高效的本地数据存储
- Canvas绘制优化，支持大量图形元素
- 虚拟滚动和懒加载减少内存占用
- 防抖和节流优化用户交互响应
